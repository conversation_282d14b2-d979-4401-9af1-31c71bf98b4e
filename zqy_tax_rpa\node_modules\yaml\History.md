
0.2.3 / 2011-12-05 
==================

  * Added a "null.yaml" test case in the examples.
  * Added support for NULL values.
  * Fixed a global leak

0.2.2 / 2011-08-31 
==================

  * Made it so the first indentation in the input determines the indentation ammount (instead of it being statically set to 2 spaces) [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]

0.2.1 / 2011-05-22 
==================

  * Fixed boolean support [<PERSON>]

0.2.0 / 2011-05-21 
==================

  * Added support for dates [<PERSON>]

0.1.1 / 2010-12-15 
==================

  * Added package.json
  * Added a regex for unquoted strings as values.
  * Fixed example/run.js so that it actually runs!
  * Fixed problem with nested hashes dedenting too much.

0.1.0 / 2010-02-25
==================

  * Added single quote string
  * Added parse error contexts
  * Added support for spaces in hash keys
  * Added basic --- support
  * Added inline hash whitespace tolerance
  * Added spec for invalid inline hash
  * Added inline hash support
  * Fixed syntax error escaping

0.0.1 / 2010-02-25
==================

  * Initial release
