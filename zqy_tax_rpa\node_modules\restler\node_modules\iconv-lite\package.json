{"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.11", "license": "MIT", "keywords": ["iconv", "convert", "charset"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON> (https://github.com/jenkinv)", "<PERSON><PERSON><PERSON> (https://github.com/adamansky)", "<PERSON> (https://github.com/stagas)", "<PERSON> (https://github.com/pekim)", "Niggler (https://github.com/Niggler)", "wychi (https://github.com/wychi)", "<PERSON> (https://github.com/david50407)", "<PERSON><PERSON><PERSON><PERSON> (https://github.com/czchen)", "<PERSON> Treveil (https://github.com/leetreveil)", "<PERSON> (https://github.com/mscdex)"], "main": "index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": ">=1.1"}}