// const pbottleRPA = require("./pbottleRPA");
// const fs = require('fs');
// const xlsx = require("node-xlsx");
// const workSheetsFromFile = xlsx.parse(`${__dirname}\\配置项.xlsx`);
// const config_sheet = workSheetsFromFile[0].data;
// const global_download_path = config_sheet[1][2];
// const Untils = require("./untils");
// const untils = new Untils();
const key = require('./chaojiying-info.js');
// pbottleRPA.moveMouseSmooth(810,400)
// pbottleRPA.moveMouseSmooth(1110,620)
// pbottleRPA.screenShot(`${global_download_path}/1.png`,810,400,300,220)
// const image = `${global_download_path}/1.png`;
// pbottleRPA.sleep(2000)
// const res = untils.recognizeText1(key[0],image)
// console.log('识别结果:', res);


const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const filename = 'captcha.jpg';

// 创建 FormData 实例
const form = new FormData();
form.append('user', key[0].user);
form.append('pass', key[0].pass);
form.append('softid', key[0].softid); // 软件ID 可在用户中心生成
form.append('codetype', '1104'); // 验证码类型 http://www.chaojiying.com/price.html 选择
form.append('userfile', fs.createReadStream(filename), {
    filename: filename,
    contentType: 'image/jpg'
});

// 发送请求
axios.post('http://upload.chaojiying.net/Upload/Processing.php', form, {
    headers: {
        ...form.getHeaders(),
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.8; rv:24.0) Gecko/20100101 Firefox/24.0'
    }
}).then(response => {
    const captcha = response.data;
    console.log('Captcha Encoded.');
    console.log(captcha);
}).catch(error => {
    console.error('Error:', error.message);
});