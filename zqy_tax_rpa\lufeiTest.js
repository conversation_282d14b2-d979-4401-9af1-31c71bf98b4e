// const pbottleRPA = require("./pbottleRPA");
// const fs = require('fs');
// const xlsx = require("node-xlsx");
// const workSheetsFromFile = xlsx.parse(`${__dirname}\\配置项.xlsx`);
// const config_sheet = workSheetsFromFile[0].data;
// const global_download_path = config_sheet[1][2];
// const Untils = require("./untils");
// const untils = new Untils();
const key = require('./chaojiying-info.js');
const rest = require('restler')
// pbottleRPA.moveMouseSmooth(810,400)
// pbottleRPA.moveMouseSmooth(1110,620)
// pbottleRPA.screenShot(`${global_download_path}/1.png`,810,400,300,220)
// const image = `${global_download_path}/1.png`;
// pbottleRPA.sleep(2000)
// const res = untils.recognizeText1(key[0],image)
// console.log('识别结果:', res);


var rest 	 = require('restler'),
	fs   	 = require('fs'),
	filename = 'captcha.gif';

rest.post('http://upload.chaojiying.net/Upload/Processing.php', {
	multipart: true,
	data: {
		'user': key[0].user,
		'pass': key[0].pass,
		'softid':key[0].softid,  //软件ID 可在用户中心生成
		'codetype': '1104',  //验证码类型 http://www.chaojiying.com/price.html 选择
		'userfile': rest.file(filename, null, fs.statSync(filename).size, null, 'image/gif') // filename: 抓取回来的码证码文件
	},
	headers: { 
		'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.8; rv:24.0) Gecko/20100101 Firefox/24.0',
		'Content-Type' : 'application/x-www-form-urlencoded' 
	}
}).on('complete', function(data) {
	var captcha = JSON.parse(data);
	console.log('Captcha Encoded.');
	console.log(captcha);
});