const pbottleRPA = require("./pbottleRPA");
const fs = require('fs');
const xlsx = require("node-xlsx");
const workSheetsFromFile = xlsx.parse(`${__dirname}\\配置项.xlsx`);
const config_sheet = workSheetsFromFile[0].data;
const global_download_path = config_sheet[1][2];
const Untils = require("./untils");
const untils = new Untils();
const key = require('./chaojiying-info.js');
main()
async function main() {
    pbottleRPA.screenShot(`${global_download_path}/1.png`,810,400,300,220)
    const image = `${global_download_path}/1.png`;
    const res = await untils.recognizeTextNew(key[0],image)
    console.log('识别结果:', res);
    console.log('识别结果:', res.pic_str);
    const textArray = res.pic_str.split('|')
	console.log(textArray)
    if(textArray.length == 4){
        for(let i=0;i<textArray.length;i++){
            console.log(textArray[i].split(',')[0],textArray[i].split(',')[1])
            pbottleRPA.moveMouseSmooth(810+Number(textArray[i].split(',')[0]),400+Number(textArray[i].split(',')[1]))
            pbottleRPA.mouseClick()
        }
        try {
            fs.unlinkSync(`${global_download_path}/1.png`);
                console.log('文件已删除。');
            } catch (err) {
                console.error('删除文件时出错:', err);
        }
    }else{
        console.log('识别失败')
    }
}
