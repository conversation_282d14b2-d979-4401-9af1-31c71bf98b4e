const Untils = require('./untils');
const key = require('./chaojiying-info.js');

async function testRecognizeText1() {
    try {
        const untils = new Untils();
        const filename = 'captcha.jpg';
        
        console.log('开始识别验证码...');
        const result = await untils.recognizeText1(key[0], filename);
        console.log('识别结果:', result);
        
        if (result && result.pic_str) {
            console.log('识别成功，验证码内容:', result.pic_str);
        } else {
            console.log('识别失败或无结果');
        }
    } catch (error) {
        console.error('测试失败:', error.message);
    }
}

// 运行测试
testRecognizeText1();
