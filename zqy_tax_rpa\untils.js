const pbottleRPA = require('./pbottleRPA')
const xlsx = require("node-xlsx");
const rest = require('restler')
const fs = require('fs')
const fss = require('fs').promises; 
const path = require('path');
const php = require("php2javascript");
const request = require('sync-request'); //默认同步请求
const {
	FormData
} = require('sync-request');
const compressing = require('compressing');
const https = require('https');
const workSheetsFromFile = xlsx.parse(`${__dirname}\\配置项.xlsx`);
const config_sheet = workSheetsFromFile[0].data;
const global_download_path = config_sheet[1][2];
let global_rpaname = config_sheet[1][0];
const {
	execSync
} = require('child_process');
const {
	timeStamp
} = require('console');

const fetch = require('node-fetch');
const FormDatas = require('form-data');
const axios = require('axios');
const qs = require('qs');
// 配置OSS客户端
const OSS = require('ali-oss');

const client = new OSS({
    endpoint: 'oss-cn-shanghai.aliyuncs.com',
    accessKeyId: 'LTAI5tEZoVaq5zroB7urbauK',
    accessKeySecret: '******************************',
    bucket: 'zhenqi-cloud',
});

const ossUrl = 'https://zqcloud.shuzutech.com'
// 超级鹰账号
const user = 'heigeheige'
const pass = '200611bnc'
const softid = 'ac235bc518d8efd394e100966b74af27'
const codetype = '9800'
class Untils {
	constructor(
		global_access_token
	) {
		this.global_apiServer = config_sheet[1][3];
		this.global_access_token = global_access_token
	}

	async waitImage(tpPath, intervalFun = () => {}, timeOut = 30) {
		// console.log('waitImage',tpPath);
		for (let index = 0; index < timeOut; index++) {
			pbottleRPA.sleep(1000)
			let position = await pbottleRPA.findScreen(tpPath)
			if (position !== false) {
				return position;
			}

			if (intervalFun() == 'stopWait') {
				console.log('stopWait from intervalFun');
				return false
			}
		}
		//error
		let frame = new Error().stack.split("\n")[2]; // change to 3 for grandparent func
		let lineNumber = frame.split(":").reverse()[1];
		let functionName = frame.split(" ")[5];
		pbottleRPA.exit(`等待图片超时 ${tpPath} line:${lineNumber} function:${functionName}`)
	}

	async existImage(tpPath, intervalFun = () => {}, timeOut = 15) {
		for (let index = 0; index < timeOut; index++) {
			pbottleRPA.sleep(500)
			let position = await pbottleRPA.findScreen(tpPath);
			if (position !== false) {
				return true; // 找到图片，返回true
			}

			if (intervalFun() == 'stopWait') {
				console.log('stopWait from intervalFun');
				return false;
			}
		}
		// 图片未找到，返回false
		return false;
	}

	async existImage2(tpPath, intervalFun = () => {}, timeOut = 1) {
		for (let index = 0; index < timeOut; index++) {
			pbottleRPA.sleep(500)
			let position = await pbottleRPA.findScreen(tpPath);
			if (position !== false) {
				return true; // 找到图片，返回true
			}

			if (intervalFun() == 'stopWait') {
				console.log('stopWait from intervalFun');
				return false;
			}
		}
		// 图片未找到，返回false
		return false;
	}

	async isImage(tpPath, timeOut = 30) {
		const startTime = Date.now(); // 记录开始时间

		while (true) {
			pbottleRPA.sleep(500);

			let position = await pbottleRPA.findScreen(tpPath);
			if (position !== false) {
				return true; // 找到图片，返回true
			}

			const currentTime = Date.now();
			const elapsedTime = Math.floor((currentTime - startTime) / 1000); // 计算已经过去的时间（秒）
			if (elapsedTime >= timeOut) {
				return false; // 超时，返回false
			}
		}
	}

	async waitImageDisappear(tpPath, intervalFun = () => {}, timeOut = 30) {
		console.log('waitImageDisappear', tpPath);
		pbottleRPA.sleep(3000)
		for (let index = 0; index < timeOut; index++) {
			pbottleRPA.sleep(1000)
			let position = await pbottleRPA.findScreen(tpPath)
			if (position === false) {
				return 'ok';
			}
			if (intervalFun() == 'stopWait') {
				console.log('stopWait from intervalFun');
				return false
			}
		}

		//error
		let frame = new Error().stack.split("\n")[2]; // change to 3 for grandparent func
		let lineNumber = frame.split(":").reverse()[1];
		let functionName = frame.split(" ")[5];
		pbottleRPA.exit(`等待图片消失超时 ${tpPath} line:${lineNumber} function:${functionName}`)
	}


	httpForLog(type, url, json = null, qs = null, hasContent = false) {
		let obj = {
			headers: {
				'Content-Type': 'application/json'
			},
		}
		if (json) {
			obj['json'] = json
		}
		if (qs) {
			obj['qs'] = qs
		}
		if (hasContent) {
			obj.headers['Content-Type'] = 'application/x-www-form-urlencoded'
		}
		let res = request(type, url, obj)
		json = JSON.parse(res.getBody('utf8'))
		return json
	}

	http(type, url, json = null, qs = null, hasContent = false) {
		let obj = {
			headers: {
				'Content-Type': 'application/json'
			},
		}
		if (json) {
			obj['json'] = json
		}
		if (qs) {
			obj['qs'] = qs
		}
		if (hasContent) {
			obj.headers['Content-Type'] = 'application/x-www-form-urlencoded'
		}
		let res = request(type, this.global_apiServer + url, obj)
		json = JSON.parse(res.getBody('utf8'))
		return json
	}

	httpfp(type, url, json = null, qs = null) {
		let obj = {
			headers: {
				'Content-Type': 'application/json'
			},
		}
		if (json) {
			obj['json'] = json
		}
		if (qs) {
			obj['qs'] = qs
		}

		//老猫本地
		// let res = request(type, 'http://*************:9034/' + url, obj)
		//测试环境 
		let res = request(type, 'https://zqytest.shuzutech.com:8688/' + url, obj)
		// let res = request(type, 'https://smartdvp.shuzutech.com/' + url, obj)
		json = JSON.parse(res.getBody('utf8'))
		return json
	}

	httpforCL(type, url, json = null, qs = null, hasContent = false) {
		let obj = {
			headers: {
				'Content-Type': 'application/json'
			},
		}
		if (json) {
			obj['json'] = json
		}
		if (qs) {
			obj['qs'] = qs
		}
		if (hasContent) {
			obj.headers['Content-Type'] = 'application/x-www-form-urlencoded'
		}
		let res = request(type, `https://openapi.shuzutech.com/rpa/ocr/` + url, obj)
		json = JSON.parse(res.getBody('utf8'))
		return json
	}

	httpforCLTest(type, url, json = null, qs = null, hasContent = false) {
		let obj = {
			headers: {
				'Content-Type': 'application/json'
			},
		}
		if (json) {
			obj['json'] = json
		}
		if (qs) {
			obj['qs'] = qs
		}
		if (hasContent) {
			obj.headers['Content-Type'] = 'application/x-www-form-urlencoded'
		}
		let res = request(type, `http://127.0.0.1:5001/rpa/ocr/` + url, obj)
		json = JSON.parse(res.getBody('utf8'))
		return json
	}

	ChaoJiYing(type, json = null, qs = null, hasContent = false) {
		let obj = {
			headers: {
				'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.8; rv:24.0) Gecko/20100101 Firefox/24.0',
				'Content-Type' : 'application/x-www-form-urlencoded'
			},
		}
		if (json) {
			obj['json'] = json
		}
		if (qs) {
			obj['qs'] = qs
		}
		if (hasContent) {
			obj.headers['Content-Type'] = 'application/x-www-form-urlencoded'
		}
		let res = request(type, `https://upload.chaojiying.net/Upload/Processing.php`, obj)
		json = JSON.parse(res.getBody('utf8'))
		return json
	}

	
	httpfpOnceAuth(type, url, json = null, qs = null) {
		let obj = {
			headers: {
				'Content-Type': 'application/json'
			},
		}
		if (json) {
			obj['json'] = json
		}
		if (qs) {
			obj['qs'] = qs
		}

		//老猫本地
		// let res = request(type, 'http://*************:9046/' + url, obj)
		//测试环境 
		let res = request(type, 'https://zqytest.shuzutech.com:8688/' + url, obj)
		// let res = request(type, 'https://smartdvp.shuzutech.com/' + url, obj)
		json = JSON.parse(res.getBody('utf8'))
		return json
	}

	async renameFile(newFileName) {

		fs.readdir(global_download_path, (err, files) => {
			if (err) {
				console.error('读取目录时发生错误：', err);
				return;
			}

			const Files = files.filter(file => file.includes('全量发票查询导出结果'));

			console.error('文件不存在');
			return;

			fs.rename(global_download_path, newFileName, (err) => {
				if (err) {
					console.error('重命名文件时出错：', err);
					return;
				}
				console.log('文件已重命名为：', newFileName);
			});
		});
	};


	// 定义一个异步函数来上传文件到OSS
	async uploadToOSS(filePath) {
			console.log("压缩")
			let file_path = global_download_path + '/../dataRPA.zip';
			await compressing.zip.compressDir(global_download_path, file_path)
			const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
			let result = '';
			for (let i = 0; i < 32; i++) {
				result += characters.charAt(Math.floor(Math.random() * characters.length));
			}

			let fileName = "/invoice/"+result+'.zip'
			let file =  ossUrl+fileName;
			try {
				await client.put(fileName, filePath);
				console.log('文件上传成功:', file);
			} catch (error) {
				console.error('文件上传失败:', error);
			}

			// 删除文件
			try {
				fs.unlinkSync(filePath);
				console.log('文件已删除:', filePath);
			} catch (error) {
				console.error('文件删除失败:', error);
			}

			return file;
	}

	async uploadFile(No = 0) {
		//压缩
		console.log("压缩")
		let file_path = global_download_path + '/../dataRPA.zip';
		await compressing.zip.compressDir(global_download_path, file_path)
		const fd = new FormData();
		fd.append('file', fs.readFileSync(file_path), file_path);

		console.log('fd',fd)

		// 老猫本地
		// let url = 'http://*************:9034/'
		// 测试环境
		let url = 'https://zqytest.shuzutech.com:8688/'
		// 生产
		// let url = 'https://smartdvp.shuzutech.com/'

		let rs = request('POST', url + 'collect/uploadOss', {
			headers: {
				"content-type": "multipart/form-data",
			},
			file: fd,
		});

		let json = JSON.parse(rs.getBody('utf8'))
		if (json.state !== 'success') {
			this.pbottleRPA.exit('文件上传失败')
		}

		global['file' + No] = json.content;
		console.log('文件链接文件', No, global['file' + No]);

		//清理目录旧文件
		fs.readdirSync(global_download_path).forEach((file) => {
			fs.unlinkSync(`${global_download_path}/${file}`);
		})

		return json.data.link
	}

	
	getRpaTodoTask() {
		console.log('获取最新节点id:')
		let untils = new Untils();
		
		// 老猫本地
		// let res = untils.http('GET', 'flow/operate/rpaTodoTask', null,{
		// 	'rpaName': global_rpaname
		// }, false)

		let res = untils.http('GET', 'flow/operate/rpaTodoTask', null,{
			'rpaName': global_rpaname
		}, false)
		console.log('最新节点', JSON.stringify(res))
		if(res.errCode !== 500 ){
			this.addLog(global.traceId,`当前流程完成情况：${JSON.stringify(res)}`,'rpaTodoTask')
		}
		return res
	}

	// result 0不通过，1通过
	completeTask( id , flowId, flowKey , taskKey, variable, result, message) {
		console.log('完成节点的id:', id)
		console.log('完成节点的flowId:', flowId)
		console.log('完成节点的flowKey:', flowKey)
		console.log('完成节点的taskKey:', taskKey)
		console.log('完成节点的variable:', variable)
		console.log('完成节点的result:', result)
		console.log('完成节点的message:', message)
		// 老猫本地
		// let res = this.http('POST', '/operate/completeTask', {
		// 	'id': id,
		// 	'flowId': flowId,
		// 	'flowKey': flowKey,
		// 	'taskKey': taskKey,
		// 	'variable': JSON.parse(variable),
		// 	"result": result,
		// 	"message": message,
		// }, null, false)

		let res = this.http('POST', 'flow/operate/completeTask', {
			'id': id,
			'flowId': flowId,
			'flowKey': flowKey,
			'taskKey': taskKey,
			'variable': typeof variable === 'string' ? JSON.parse(variable) : variable,
			"result": result,
			"message": message,
		}, null, false)
		this.addLog(global.traceId, `完成任务的message:${message},后台完成接口响应结果:${JSON.stringify(res)}`, `${taskKey}`)
		console.log('完成结果', JSON.stringify(res))
		return res
	}


	terminateTask(id,status,variable,message) {
		console.log('终止任务的flowId:', id)
		console.log('终止任务的status:', status)
		console.log('终止任务的variable:', variable)
		console.log('终止任务的message:', message)

		// 老猫本地
		// let res = this.http('POST', '/operate/terminate', {
		// 	"id": id,
		// 	"status": status,
		// 	"variable": variable,
		// }, null, false)

		let res = this.http('POST', 'flow/operate/terminate', {
			"id": id,
			"status": status,
			"variable": variable,
			"message": message,
		}, null, false)
		this.addLog(global.traceId, `终止任务的message:${message},后台终止接口响应结果:${JSON.stringify(res)}`, `terminateTask`)
		console.log('结束任务结果', JSON.stringify(res))
		return res
	}

	getFs() {
		pbottleRPA.sleep(1000)
		return fs
	}

	async checkLogin() {
		var loginMesg = await pbottleRPA.browserCMD_text(
			`body > div.el-message.el-message--warning.el-icon-warning-color > p`)
		if (loginMesg != 'ok') {
			console.log("密码报错", loginMesg)
			throw new Error(loginMesg)
		}
		var loginMesg1 = await pbottleRPA.browserCMD_text(`div[aria-label="提示"] p`)
		if (loginMesg1 != 'ok') {
			console.log("密码报错1", loginMesg1)
			throw new Error(loginMesg1)
		}

	}

	async checkError() {
		// 校验输入内容
		// body > div.el-message.el-message--warning.el-icon-warning-color > p  所查询的自然人信息不存在，请核验输入信息是否有误  您添加的自然人未注册或证件号码输入错误，无法添加。
		// body > div.el-message.el-message--warning.el-icon-warning-color > p 姓名与身份号不匹配
		// body > div.el-message.el-message--warning.el-icon-warning-color   重复
		var errorMesg = await pbottleRPA.browserCMD_text(
			`body > div.el-message.el-message--warning.el-icon-warning-color > p`)
		if (errorMesg != 'ok') {
			console.log("报错------", errorMesg)
			throw new Error(errorMesg)
		}

		var errorInput = await pbottleRPA.browserCMD_text(`div.el-form-item__error`)
		if (errorInput != 'ok') {
			console.log("报错1------", errorInput)
			throw new Error(errorInput)
		}

		let ele =
			`div[aria-label="提示"] button[class='el-button el-button--default el-button--small el-button--primary ']`

		if (ele) {
			// 点击提交后会弹出提示按钮，点击提示，即可完成添加，或者弹出重复添加（关联关系已存在）
			await pbottleRPA.browserCMD_click(
				`div[class="success_prompt_dialog_footer"] button[class='el-button el-button--default']`
			)
			var errorMesg = await pbottleRPA.browserCMD_text(
				`body > div.el-message.el-message--warning.el-icon-warning-color > p`)
			if (errorMesg != 'ok') {
				console.log("报错2------", errorMesg)
				throw new Error("办税员已存在")
			}

		}

	}

	// 获取小号验证码
	getTelxCode(telx) {
		let url = `https://smartdvp.shuzutech.com/taxmanage/work/number/getFiveMinutesNewCode?telX=${telx}`
		let res = request('GET', url);
		console.log('获取telx验证码内容', res.body.toString())
		return res.body.toString()
	}

	async checkDownloadedFiles() {
		pbottleRPA.sleep(5000)
		return new Promise((resolve, reject) => {
			fs.readdir(global_download_path, (err, files) => {
				if (err) {
					console.error('读取目录时发生错误：', err);
					reject("读取目录时发生错误" + err)
				}

				const unfinishedFiles = files.filter(file => file.includes('未确认'));

				if (unfinishedFiles.length > 0) {
					console.log('存在未确认文件，继续等待...');
					setTimeout(() => {
						this.checkDownloadedFiles().then(resolve).catch(reject);
					}, 1000); // 每隔一秒钟重新检查
				} else {
					console.log('所有文件已确认下载完成');
					resolve("success")
				}
			});
		})
	}

	async waitForElementValue(selector, timeout, callback) {
		var startTime = Date.now();
		var checkExist = setInterval(function() {
			var elementValue = pbottleRPA.browserCMD_text(selector);
			if (elementValue) {
				clearInterval(checkExist);
				callback(elementValue);
			}
			var currentTime = Date.now();
			if (currentTime - startTime >= timeout) {
				clearInterval(checkExist);
				console.log("超时：未能在规定时间内获取元素值");
			}
		}, 100);
	}

	waitForElementAndGetValue(elementSelector, delay) {
		var checkExist = setInterval(function() {
			if (elementSelector.length) {
				clearInterval(checkExist);
				// 在元素出现后执行你的操作
				var dataUrl = $(elementSelector).attr('data-url');
				console.log("dataUrl999", dataUrl);
				pbottleRPA.openURL(dataUrl);
			}
		}, delay); // 每100毫秒检查一次元素是否存在
	}


	getCurrentWindowUrl() {
		try {
			// 执行 PowerShell 脚本获取当前活动窗口的 URL，并将输出编码为 Base64
			const base64Url = execSync(
				`powershell -command "(Get-Process -Name msedge | Where-Object {$_.MainWindowTitle -ne ''}).MainWindowTitle | Out-String | ForEach-Object {[Convert]::ToBase64String([System.Text.Encoding]::Unicode.GetBytes($_))}"`, {
					encoding: 'utf-8'
				});

			// 解码 Base64转换为字符串
			const decodedUrl = Buffer.from(base64Url, 'base64').toString('utf-16le');

			// 提取第一个页面的标题
			const pageTitle = decodedUrl.trim().split('和另外')[0].trim();
			return pageTitle; // 返回提取的标题
		} catch (error) {
			console.error('获取当前窗口 URL 失败:', error);
			return null;
		}
	}



	// 下载财务报表具体信息
	clickAndDown() {
		let num = pbottleRPA.browserCMD_text(`div.t-table__pagination-wrap > div > div > div.t-pagination__total`)
		if (num != 'ok') {
			const match = num.match(/\d+/)
			const number = parseInt(match[0], 10);
			if (number < 10) {
				for (let index = 1; index <= number; index++) {
					pbottleRPA.browserCMD_click(
						`div.t-table__content > table > tbody > tr:nth-child(${index}) > td:nth-child(2) a`)
					pbottleRPA.sleep(2000)
					let daochu = this.existImage("/input/1920/daochu.png")
					if (daochu === true) {
						console.log(`点击了${index}`)
						pbottleRPA.browserCMD_click(
							`body > section > section > section > main > div > div > form > div > div:nth-child(2) > button`
						)
						pbottleRPA.sleep(2000)
						pbottleRPA.keyTap('Ctrl + W')
					}

				}

			}
		}

	}


	// 增值税详细下载
	zzsDown() {
		let num = pbottleRPA.browserCMD_text(
			`div > div.t-table__pagination-wrap > div > div > div.t-pagination__total`)
		if (num != 'ok') {
			const match = num.match(/\d+/)
			const number = parseInt(match[0], 10);

			// 选择单个信息 点击进入下载
			for (let index2 = 1; index2 <= number; index2++) {
				let text = pbottleRPA.browserCMD_text(
					`div > div.t-table__content > table > tbody > tr:nth-child(${index2}) > td:nth-child(2) > a > span`
				)
				if (text.includes('增值税')) {
					pbottleRPA.browserCMD_click(
						`div > div.t-table__content > table > tbody > tr:nth-child(${index2}) > td:nth-child(2) > a > span`
					)
					let sbdaochu = this.existImage("/input/1920/sbdaochu.png")
					if (sbdaochu === true) {
						pbottleRPA.browserCMD_click(
							`div.com-sub-content > div > div > div > div > div.upper > div.right > button > span > span`
						)
						console.log(`下载了第${index2}个`)
					} else {
						pbottleRPA.browserCMD_click(
							`div.com-sub-content > div > div > div > div > div.upper > div.right > button > span > span`
						)
					}
					pbottleRPA.sleep(3000)
					pbottleRPA.keyTap('ALT+LEFT')
					break;
				}

			}
		}

	}

	// 所得税详细下载
	sdsDown() {
		let num = pbottleRPA.browserCMD_text(
			`div > div.t-table__pagination-wrap > div > div > div.t-pagination__total`)
		if (num != 'ok') {
			const match = num.match(/\d+/)
			const number = parseInt(match[0], 10);

			pbottleRPA.browserCMD_click(
				`div > div.t-table__pagination-wrap > div > div > div.t-select__wrap.t-pagination__select > div > div > div > input`
			)
			pbottleRPA.sleep(500)
			pbottleRPA.browserCMD_click(`div > div > div > ul > li:nth-child(4) > span`)
			pbottleRPA.sleep(500)

			// 选择单个信息 点击进入下载
			for (let index2 = 1; index2 <= number; index2++) {
				let text = pbottleRPA.browserCMD_text(
					`div > div.t-table__content > table > tbody > tr:nth-child(1) > td:nth-child(2) > a > span`
				)
				if (text.includes('中华人民共和国')) {
					pbottleRPA.browserCMD_click(
						`div > div.t-table__content > table > tbody > tr:nth-child(1) > td:nth-child(2) > a > span`
					)
					pbottleRPA.sleep(1000)
					let sdsdaochu = this.existImage("/input/1920/sdsdaochu.png")
					if (sdsdaochu === true) {
						pbottleRPA.browserCMD_click(
							`button span:contains(导出)`
						)
						console.log(`下载了第${index2}个`)
					} else {
						pbottleRPA.browserCMD_click(
							`button span:contains(导出)`
						)
					}
					pbottleRPA.sleep(3000)
					pbottleRPA.keyTap('CTRL+W')
				}
				pbottleRPA.browserCMD_remove(`div > div.t-table__content > table > tbody > tr:nth-child(1)`)
			}
		}


	}

	// 税款征收信息详细下载
	skzsxxDown(startDate, endDate) {
		let num = pbottleRPA.browserCMD_text(
			`div > div.t-table__pagination-wrap > div > div > div.t-pagination__total`)
		if (num != 'ok') {
			const match = num.match(/\d+/)
			const number = parseInt(match[0], 10);
			let page = Math.ceil(number / 10)
			console.log('page', page)
			for (let index = 1; index <= page; index++) {
				if (index > 1) {
					pbottleRPA.browserCMD_click(
						`div.t-table__pagination-wrap > div > div > div.t-pagination__btn.t-pagination__btn-next`
						)
					pbottleRPA.sleep(500)
				}
				let html = pbottleRPA.browserCMD_html(
					'div.com-sub-content > div > div > div > div > div > div.bottom > div.table-container > div > div.t-table__content > table'
				)
				this.getFs().writeFileSync(global_download_path + `/${startDate}-${endDate}-${index}-skzs.html`,
					html)
			}
		}

	}


	// 财税-申报信息-增值税查询接口
	TaxDataQuery(cookie, payload) {
		return fetch("https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sjswszzh/zhcx/v1/DescribeSbmxcx2", {
				"headers": {
					"accept": "application/json;charset=UTF-8",
					"accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
					"content-type": "application/json",
					"sec-ch-ua": "\"Microsoft Edge\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
					"sec-ch-ua-mobile": "?0",
					"sec-ch-ua-platform": "\"Windows\"",
					"sec-fetch-dest": "empty",
					"sec-fetch-mode": "cors",
					"sec-fetch-site": "same-origin",
					"x-client-id": "fkh_mall",
					"cookie": cookie,
					"Referer": "https://etax.jiangsu.chinatax.gov.cn:8443/szzh/zhcx/sbxx/sbxxcx",
					"Referrer-Policy": "strict-origin-when-cross-origin"
				},
				"body": JSON.stringify(payload),
				"method": "POST"
			})
			.then(response => response.json())
			.then(data => {
				if (data.Response && data.Response.Data.List) {
					console.log("增值税查询结果", data.Response.Data.List);
					return data.Response.Data.List; // 返回结果
				} else {
					console.error("增值税查询无结果！");
					return null;
				}
			})
			.catch(error => {
				console.error("增值税查询失败，错误信息：", error);
				return null;
			});
	}

	// 财税-申报信息-增值税下载接口
	TaxDataDown(cookie, payload) {
		return fetch("https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sjswszzh/zhcx/v1/exportSbmxxqcx", {
				"headers": {
					"accept": "application/json;charset=UTF-8",
					"accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
					"content-type": "application/json",
					"sec-ch-ua": "\"Microsoft Edge\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
					"sec-ch-ua-mobile": "?0",
					"sec-ch-ua-platform": "\"Windows\"",
					"sec-fetch-dest": "empty",
					"sec-fetch-mode": "cors",
					"sec-fetch-site": "same-origin",
					"x-client-id": "fkh_mall",
					"cookie": cookie,
					"Referer": "https://etax.jiangsu.chinatax.gov.cn:8443/szzh/zhcx/sbxx/sbxxcx/detail?isCyqy=false",
					"Referrer-Policy": "strict-origin-when-cross-origin"
				},
				"body": JSON.stringify(payload),
				"method": "POST"
			})
			.then(response => response.json())
			.then(data => {
				if (data.Response && data.Response.Data) {
					console.log("增值税下载结果！", data);
					return data.Response.Data; // 返回结果
				} else {
					console.error("增值税下载无结果！");
					return null;
				}
			})
			.catch(error => {
				console.error("增值税下载失败，错误信息：", error);
				return null;
			});
	}

	// 财税-申报信息-所得税下载接口
	IncomeDataDown(cookie, payload) {
		return fetch("https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sbss/ssmx/sb/v1/downloadPdf?djxh=null", {
				"headers": {
					"accept": "application/json, text/plain, */*",
					"accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
					"content-type": "application/json",
					"gt4_dzswj_subsession_id": "",
					"sec-ch-ua": "\"Microsoft Edge\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
					"sec-ch-ua-mobile": "?0",
					"sec-ch-ua-platform": "\"Windows\"",
					"sec-fetch-dest": "empty",
					"sec-fetch-mode": "cors",
					"sec-fetch-site": "same-origin",
					"cookie": cookie,
					"Referer": "https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sbss/view/",
					"Referrer-Policy": "strict-origin-when-cross-origin"
				},
				"body": JSON.stringify(payload),
				"method": "POST"
			})
			.then(response => response.json())
			.then(data => {
				console.log("年度无结果", data);
				if (data.Response && data.Response.Data) {
					console.log("所得税下载结果！", data.Response.Data);
					return data.Response.Data; // 返回结果
				} else {
					console.error("所得税下载无结果！");
					return null;
				}
			})
			.catch(error => {
				console.error("所得税下载失败，错误信息：", error);
				return null;
			});
	}

	// 财税-财务报表-查询接口
	financialDataQuery(timeStamp, cookie, payload) {
		return fetch(
				`https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sbss/ssmx/zlbs/v1/queryZlbscjb?_=${timeStamp}`, {
					"headers": {
						"accept": "application/json, text/plain, */*",
						"accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
						"content-type": "application/json",
						"requestid": timeStamp,
						"sec-ch-ua": "\"Microsoft Edge\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
						"sec-ch-ua-mobile": "?0",
						"sec-ch-ua-platform": "\"Windows\"",
						"sec-fetch-dest": "empty",
						"sec-fetch-mode": "cors",
						"sec-fetch-site": "same-origin",
						"x-b3-sampled": "1",
						"x-b3-spanid": "20e7db5dbe55ee4e",
						"x-b3-traceid": "20e7db5dbe55ee4e",
						"x-tsf-client-timestamp": timeStamp,
						"cookie": cookie,
						"Referer": "https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sbss/view/",
						"Referrer-Policy": "strict-origin-when-cross-origin"
					},
					"body": JSON.stringify(payload),
					"method": "POST"
				})
			.then(response => response.json())
			.then(data => {
				if (data.Response && data.Response.Data.Result.mxList) {
					console.log("查询结果", data.Response.Data.Result.mxList);
					return data.Response.Data.Result.mxList; // 返回结果
				} else {
					console.error("查询无结果！");
					return null;
				}
			})
			.catch(error => {
				console.error("查询失败，错误信息：", error);
				return null;
			});
	}


	// 财税-财务报表-下载接口
	financialDataDown(djxh, cookie, payload) {
		return fetch(
				`https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sbss/ssmx/sb/v1/downloadPdf?djxh=${djxh}`, {
					"headers": {
						"accept": "application/json, text/plain, */*",
						"accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
						"content-type": "application/json",
						"gt4_dzswj_subsession_id": "",
						"sec-ch-ua": "\"Microsoft Edge\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
						"sec-ch-ua-mobile": "?0",
						"sec-ch-ua-platform": "\"Windows\"",
						"sec-fetch-dest": "empty",
						"sec-fetch-mode": "cors",
						"sec-fetch-site": "same-origin",
						"cookie": cookie,
						"Referer": "https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sbss/view/",
						"Referrer-Policy": "strict-origin-when-cross-origin"
					},
					"body": JSON.stringify(payload),
					"method": "POST"
				})
			.then(response => response.json())
			.then(data => {
				if (data.Response && data.Response.Data) {
					console.log("财务报表下载结果！", data.Response.Data);
					return data.Response.Data; // 返回结果
				} else {
					console.error("财务报表下载无结果！");
					return null;
				}
			})
			.catch(error => {
				console.error("财务报表下载失败，错误信息：", error);
				return null;
			});

	}


	// 财税-纳税信用评价-查询接口
	taxRatLevleDataQuery(timeStamp, cookie, payload) {
		return fetch(
				`https://etax.jiangsu.chinatax.gov.cn:8443/ssjg/api/zhssjg/nsxygl/queryNdpjjg?djxh=&_=${timeStamp}`, {
					"headers": {
						"accept": "application/json, text/plain, */*",
						"accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
						"content-type": "application/json",
						"djxh": "undefined",
						"requestid": timeStamp,
						"sec-ch-ua": "\"Microsoft Edge\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
						"sec-ch-ua-mobile": "?0",
						"sec-ch-ua-platform": "\"Windows\"",
						"sec-fetch-dest": "empty",
						"sec-fetch-mode": "cors",
						"sec-fetch-site": "same-origin",
						"x-b3-sampled": "1",
						"x-b3-spanid": "30c3e166d964904d",
						"x-b3-traceid": "30c3e166d964904d",
						"x-tsf-client-timestamp": timeStamp,
						"cookie": cookie,
						"Referer": "https://etax.jiangsu.chinatax.gov.cn:8443/ssjg/view/zhssjg/",
						"Referrer-Policy": "strict-origin-when-cross-origin"
					},
					"body": JSON.stringify(payload),
					"method": "POST"
				})
			.then(response => response.json())
			.then(data => {
				if (data.Response && data.Response.Data.Result) {
					console.log("纳税信用评价查询结果", data.Response.Data.Result);
					return data.Response.Data.Result; // 返回结果
				} else {
					console.error("纳税信用评价查询无结果！");
					return null;
				}
			})
			.catch(error => {
				console.error("纳税信用评价查询失败，错误信息：", error);
				return null;
			});
	}


	// 财税-纳税信用评价-下载接口
	taxRatLevleDataDown(timeStamp, cookie, payload) {
		return fetch(
				`https://etax.jiangsu.chinatax.gov.cn:8443/ssjg/api/zhssjg/nsxygl/downloadPjjgcjxx?djxh=&_=${timeStamp}`, {
					"headers": {
						"accept": "application/json, text/plain, */*",
						"accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
						"content-type": "application/json",
						"djxh": "undefined",
						"requestid": timeStamp,
						"sec-ch-ua": "\"Microsoft Edge\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
						"sec-ch-ua-mobile": "?0",
						"sec-ch-ua-platform": "\"Windows\"",
						"sec-fetch-dest": "empty",
						"sec-fetch-mode": "cors",
						"sec-fetch-site": "same-origin",
						"x-b3-sampled": "1",
						"x-b3-spanid": "c3321f1306a26b4d",
						"x-b3-traceid": "c3321f1306a26b4d",
						"x-tsf-client-timestamp": timeStamp,
						"cookie": cookie,
						"Referer": "https://etax.jiangsu.chinatax.gov.cn:8443/ssjg/view/zhssjg/",
						"Referrer-Policy": "strict-origin-when-cross-origin"
					},
					"body": JSON.stringify(payload),
					"method": "POST"
				})
			.then(response => response.json())
			.then(data => {
				if (data.Response && data.Response.Data) {
					console.log("纳税信用评价下载结果！", data.Response.Data);
					return data.Response.Data; // 返回结果
				} else {
					console.error("纳税信用评价下载无结果！");
					return null;
				}
			})
			.catch(error => {
				console.error("纳税信用评价下载失败，错误信息：", error);
				return null;
			});

	}

	// 财税-纳税信用评价-下载接口,二进制数据处理
	taxRatLevleDataBareyDown(barey, cookie, payload) {
		return fetch(
				`https://etax.jiangsu.chinatax.gov.cn:8443/swws/api/swws/wscx/getPdfFileByte/${barey}`, {
					"headers": {
						"accept": "*/*",
						"accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
						"sec-ch-ua": "\"Microsoft Edge\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
						"sec-ch-ua-mobile": "?0",
						"sec-ch-ua-platform": "\"Windows\"",
						"sec-fetch-dest": "empty",
						"sec-fetch-mode": "cors",
						"sec-fetch-site": "same-origin",
						"cookie": cookie,
						// "Referer": `https://etax.jiangsu.chinatax.gov.cn:8443/swws/view/swws/pdf/web/viewer.html?file=/swws/api/swws/wscx/getPdfFileByte/${barey}&filename=2023%E5%B9%B4%E5%BA%A6%E7%BA%B3%E7%A8%8E%E4%BF%A1%E7%94%A8%E8%AF%84%E4%BB%B7%E4%BF%A1%E6%81%AF`,
						// "Referrer-Policy": "strict-origin-when-cross-origin"
					},
					"body": null,
					"method": "GET"
				})
			.then(response => response.arrayBuffer())
			.then(buffer => {
				const base64Data = Buffer.from(buffer).toString('base64');
				return base64Data;
			})
			.catch(error => {
				console.error("纳税信用评价二进制下载失败，错误信息：", error);
				return null;
			});

	}

	// 财税-征收信息查询
	TaxJkxxQuery(cookie, payload) {
		return fetch("https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sjswszzh/zhcx/jkxx/v1/DescribeJkxxcx", {
				"headers": {
					"accept": "application/json;charset=UTF-8",
					"accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
					"content-type": "application/json",
					"sec-ch-ua": "\"Microsoft Edge\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
					"sec-ch-ua-mobile": "?0",
					"sec-ch-ua-platform": "\"Windows\"",
					"sec-fetch-dest": "empty",
					"sec-fetch-mode": "cors",
					"sec-fetch-site": "same-origin",
					"x-client-id": "fkh_mall",
					"cookie": cookie,
					"Referer": "https://etax.jiangsu.chinatax.gov.cn:8443/szzh/zhcx/jkxx/jkxxcx",
					"Referrer-Policy": "strict-origin-when-cross-origin"
				},
				"body": JSON.stringify(payload),
				"method": "POST"
			})
			.then(response => response.json())
			.then(data => {
				if (data.Response.Data) {
					console.log("征收信息查询结果", data.Response.Data.List);
					return data.Response.Data.List; // 返回结果
				} else {
					console.error("征收信息查询无结果！");
					return null;
				}
			})
			.catch(error => {
				console.error("征收信息查询失败，错误信息：", error);
				return null;
			});
	}


	// 财税-纳税人信息查询
	taxPayerInfoQuery(queryType, cookie) {
		return fetch(
				`https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sjswszzh/zhcx/nsrxx/v1/${queryType}?pageNum=1&pageSize=50&djxh=`, {
					"headers": {
						"accept": "application/json;charset=UTF-8",
						"accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
						"sec-ch-ua": "\"Microsoft Edge\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
						"sec-ch-ua-mobile": "?0",
						"sec-ch-ua-platform": "\"Windows\"",
						"sec-fetch-dest": "empty",
						"sec-fetch-mode": "cors",
						"sec-fetch-site": "same-origin",
						"x-client-id": "fkh_mall",
						"cookie": cookie,
						"Referer": "https://etax.jiangsu.chinatax.gov.cn:8443/szzh/zhcx/nsrxxcx",
						"Referrer-Policy": "strict-origin-when-cross-origin"
					},
					"body": null,
					"method": "POST"
				})
			.then(response => response.json())
			.then(data => {
				console.log("*********", data.Response.Data);
				if (data.Response.Data) {
					console.log("纳税人信息查询结果", data.Response.Data);
					return data.Response.Data; // 返回结果
				} else {
					console.error("纳税人信息查询无结果！");
					return null;
				}
			})
			.catch(error => {
				console.error("纳税人信息查询失败，错误信息：", error);
				return null;
			});
	}

	// 财税-纳税人违法违章信息查询
	illegalInfoQuery(cookie, payload) {
		console.log("88", payload)
		return fetch(
				"https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sjswszzh/gycxfw/v1/queryDataList/CX_SJSZZH_0002_WFWZXXCX", {
					"headers": {
						"accept": "application/json;charset=UTF-8",
						"accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
						"content-type": "application/json",
						"sec-ch-ua": "\"Microsoft Edge\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
						"sec-ch-ua-mobile": "?0",
						"sec-ch-ua-platform": "\"Windows\"",
						"sec-fetch-dest": "empty",
						"sec-fetch-mode": "cors",
						"sec-fetch-site": "same-origin",
						"x-client-id": "fkh_mall",
						"cookie": cookie,
						"Referer": "https://etax.jiangsu.chinatax.gov.cn:8443/szzh/zhcx/wfwzxxcx",
						"Referrer-Policy": "strict-origin-when-cross-origin"
					},
					"body": JSON.stringify(payload),
					"method": "POST"
				})
			.then(response => response.json())
			.then(data => {
				console.log("*********", data.Response.Data);
				if (data.Response.Data) {
					console.log("纳税人违法违章信息查询结果", data.Response.Data);
					return data.Response.Data; // 返回结果
				} else {
					console.error("纳税人违法违章信息查询无结果！");
					return null;
				}
			})
			.catch(error => {
				console.error("纳税人违法违章信息查询失败，错误信息：", error);
				return null;
			});
	}

	// 财税-欠税信息查询接口
	taxArrearsInfoQuery(cookie, payload) {
		return fetch("https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sjswszzh/zhcx/qsxx/v1/DescribeQsxxcx", {
				"headers": {
					"accept": "application/json;charset=UTF-8",
					"accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
					"content-type": "application/json",
					"sec-ch-ua": "\"Microsoft Edge\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
					"sec-ch-ua-mobile": "?0",
					"sec-ch-ua-platform": "\"Windows\"",
					"sec-fetch-dest": "empty",
					"sec-fetch-mode": "cors",
					"sec-fetch-site": "same-origin",
					"x-client-id": "fkh_mall",
					"cookie": cookie,
					"Referer": "https://etax.jiangsu.chinatax.gov.cn:8443/szzh/zhcx/qsxx/qsxxcx",
					"Referrer-Policy": "strict-origin-when-cross-origin"
				},
				"body": JSON.stringify(payload),
				"method": "POST"
			})
			.then(response => response.json())
			.then(data => {
				if (data.Response && data.Response.Data.List) {
					console.log("欠税信息查询结果", data.Response.Data.List);
					return data.Response.Data.List; // 返回结果
				} else {
					console.error("欠税信息查询无结果！");
					return null;
				}
			})
			.catch(error => {
				console.error("欠税信息查询失败，错误信息：", error);
				return null;
			});
	}

	// 财税-欠税信息下载接口
	taxArrearsInfoDown(cookie, payload) {
		return fetch("https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sjswszzh/zhcx/qsxx/v1/ExportQsxxcx", {
				"headers": {
					"accept": "application/json;charset=UTF-8",
					"accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
					"content-type": "application/json",
					"sec-ch-ua": "\"Microsoft Edge\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
					"sec-ch-ua-mobile": "?0",
					"sec-ch-ua-platform": "\"Windows\"",
					"sec-fetch-dest": "empty",
					"sec-fetch-mode": "cors",
					"sec-fetch-site": "same-origin",
					"x-client-id": "fkh_mall",
					"cookie": cookie,
					"Referer": "https://etax.jiangsu.chinatax.gov.cn:8443/szzh/zhcx/qsxx/qsxxcx",
					"Referrer-Policy": "strict-origin-when-cross-origin"
				},
				"body": JSON.stringify(payload),
				"method": "POST"
			})
			.then(response => response.json())
			.then(data => {
				if (data.Response && data.Response.Data) {
					return data.Response.Data; // 返回结果
				} else {
					console.error("欠税信息下载无结果！");
					return null;
				}
			})
			.catch(error => {
				console.error("欠税信息下载失败，错误信息：", error);
				return null;
			});
	}

	// 财税-解析Base64，转化为PDF并存入指定目录
	BaseDcrypt(fileName, base64Data, startDate, endDate) {

		const base64String = base64Data.replace(/^data:application\/pdf;base64,/, '');
		const buffer = Buffer.from(base64String, 'base64');
		const outputDirectory = path.join(global_download_path, '');
		const outputFilePath = path.join(outputDirectory, fileName + startDate + '--' + endDate + '.pdf');

		fs.writeFile(outputFilePath, buffer, (err) => {
			if (err) {
				console.error('保存文件失败:', err);
			} else {
				console.log('文件已成功保存为 PDF，路径:', outputFilePath);
			}
		});
	}

	// 财税-解析Base64，转化为PDF并存入指定目录,纳税信用评价方法单独写
	BaseDcrypt1(fileName, base64Data) {

		const base64String = base64Data.replace(/^data:application\/pdf;base64,/, '');
		const buffer = Buffer.from(base64String, 'base64');
		const outputDirectory = path.join(global_download_path, '');
		const outputFilePath = path.join(outputDirectory, fileName + '.pdf');

		fs.writeFile(outputFilePath, buffer, (err) => {
			if (err) {
				console.error('保存文件失败:', err);
			} else {
				console.log('文件已成功保存为 PDF，路径:', outputFilePath);
			}
		});
	}

	async uploadPdf(filePath) {

		const form = new FormDatas();
		const fileName = path.basename(filePath);

		form.append('files', fs.createReadStream(filePath), {
			filename: fileName
		});

		// 配置请求头
		const headers = {
			...form.getHeaders(), // form-data 会自动生成正确的 headers
			'Accept': 'text/plain, */*; q=0.01',
			'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
			'Sec-Ch-Ua': '"Not A(Brand";v="8", "Chromium";v="132", "Microsoft Edge";v="132"',
			'Sec-Ch-Ua-Mobile': '?0',
			'Sec-Ch-Ua-Platform': '"Windows"',
			'Sec-Fetch-Dest': 'empty',
			'Sec-Fetch-Mode': 'cors',
			'Sec-Fetch-Site': 'same-origin',
			'X-Requested-With': 'XMLHttpRequest',
			'Cookie': 'sid=dcdddb8b-9710-4fb3-bcd8-45ced0b32c1d; _gcl_au=1.1.640808891.1737703554; _gid=GA1.2.580213480.1737703555; _gat_UA-97830640-1=1; _ga_31ED9S1DJ2=GS1.1.1737708087.2.1.1737708320.0.0.0; _ga=GA1.2.239336042.1737703554',
			'Referer': 'https://www.pdfpai.com/pdf-to-excel',
			'Referrer-Policy': 'strict-origin-when-cross-origin',
		};

		// 发送 POST 请求上传文件
		try {
			const response = await axios.post('https://www.pdfpai.com/pdf/uploadFiles', form, {
				headers
			});

			if (response && response.data) {
				return response.data; // 返回上传后的数据
			} else {
				console.error('文件上传无结果！');
				return null;
			}
		} catch (error) {
			console.error(`文件上传失败: ${filePath}`, error);
			return null; // 如果发生错误，返回 null
		}

	}

	async PdfToExcel(url, index, STATUS) {
		const formData = new FormDatas();

		// 使用 append 方法添加表单字段
		formData.append('url', url);
		formData.append('index', index);
		formData.append('pid', '1');
		formData.append('oid', '3');
		formData.append('status', STATUS);
		formData.append('pwd', '');
		formData.append('formatv1', '1');
		formData.append('formatv2', '1');

		const headers = {
			"accept": "application/json, text/javascript, */*; q=0.01",
			"accept-language": "zh-CN,zh;q=0.9",
			"x-requested-with": "XMLHttpRequest",
			"cookie": "sid=059b2bee-1266-485e-915f-f21e8c410ce7; _gcl_au=1.1.2145210307.1737702585; _gid=GA1.2.105564383.1737702585; _ga=GA1.2.934084918.1737702585; _ga_31ED9S1DJ2=GS1.1.1737702585.1.1.1737703215.0.0.0; _gat_UA-97830640-1=1",
			"Referer": "https://www.pdfpai.com/pdf-to-excel",
			"Referrer-Policy": "strict-origin-when-cross-origin",
			...formData.getHeaders()
		};

		try {
			const response = await axios.post(`https://www.pdfpai.com/pdf/doProcess.do`, formData, {
				headers: headers,
				maxRedirects: 0, // 如果你想要避免重定向
			});

			console.log('转换数据:', response.data);
			if (response && response.data) {
				return response.data; // 返回上传后的数据
			} else {
				console.error('文件上传无结果！');
				return null;
			}

		} catch (error) {
			console.error('请求失败:', error);
		}
	}

	downloadJobResult(index) {
		const params = new URLSearchParams();
		params.append('index', index);
		return fetch(`https://www.pdfpai.com/pdf/fileDownload`, {
				method: 'POST',
				headers: {
					"accept": "application/json, text/javascript, */*; q=0.01",
					"accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
					"content-type": "application/x-www-form-urlencoded",
					"sec-ch-ua": "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Microsoft Edge\";v=\"132\"",
					"sec-ch-ua-mobile": "?0",
					"sec-ch-ua-platform": "\"Windows\"",
					"sec-fetch-dest": "empty",
					"sec-fetch-mode": "cors",
					"sec-fetch-site": "same-origin",
					"x-requested-with": "XMLHttpRequest",
					"cookie": "sid=dcdddb8b-9710-4fb3-bcd8-45ced0b32c1d; _gcl_au=1.1.640808891.1737703554; _gid=GA1.2.580213480.1737703555; _gat_UA-97830640-1=1; JSESSIONID=AAFD2C1456393C590BDB1D50C068350C; _ga_31ED9S1DJ2=GS1.1.1737772335.4.1.1737772364.0.0.0; _ga=GA1.2.239336042.1737703554",
					"Referer": "https://www.pdfpai.com/downToExcel",
					"Referrer-Policy": "strict-origin-when-cross-origin"
				},
				body: params,
			})
			.then(response => response.json())
			.then(jsonData => {
				try {
					console.log('EXCEL下载结果:', jsonData);
					return jsonData;
				} catch (error) {
					console.error('EXCEL下载无结果:', error);
				}
			})
			.catch(error => {
				console.error('EXCEL下载 failed', error);
			});
	}

	async uploadPdfsFromDirectory(dirPath) {
		try {
			const files = await fss.readdir(dirPath);
			const pdfFiles = files.filter(file => path.extname(file).toLowerCase() === '.pdf'); //遍历目录 找PDF文件

			for (const file of pdfFiles) {
				const filePath = path.join(dirPath, file);
				console.log(`正在上传文件: ${filePath}`);

				const result = await this.uploadPdf(filePath); //上传文件
				if (result.hasOwnProperty('STATUS') && result.STATUS == '0') {
					// 开始转换文件url, index, STATUS, num
					const resultEx = await this.PdfToExcel(result.url, result.index, result.STATUS);
					console.log(`文件转换成功: ${filePath}`, resultEx, resultEx.STATUS, resultEx.index);
					if (resultEx.STATUS === '0') {
						//开始下载转换的文件
						let isValid = false;
						while (!isValid) {
							let resultAf = await this.downloadJobResult(resultEx.index);
							if (resultAf && resultAf.hasOwnProperty('url')) {
								isValid = true;
								console.log('downloadJobResult返回的数据符合条件:', resultAf);
								// 定义文件保存路径
								let fileName = path.basename(resultAf.url);
								let savePath = path.join(global_download_path, fileName);
								https.get(`https://www.pdfpai.com/${resultAf.url}`, (response) => {
									let fileStream = fs.createWriteStream(savePath);
									response.pipe(fileStream);
									fileStream.on('finish', () => {
										console.log('EXCEL文件下载完成');
									});
								});
							} else {
								console.log('返回数据不符合条件，重新执行downloadJobResult', resultAf);
								pbottleRPA.sleep(3000)
							}
						}
					}
				}
			}

		} catch (err) {
			console.error('执行失败:', err);
		}
	}
	//关闭多余页面 
	closeOtherPages(url){
		pbottleRPA.keyTap('ctrl+l')
		pbottleRPA.keyTap('ctrl+c')
		let currentUrl = pbottleRPA.getClipboard()
		console.log('当前页面url:',currentUrl)
		if (!currentUrl.includes('chinatax.gov.cn')){
			pbottleRPA.keyTap('ctrl+w')
		}
		pbottleRPA.keyTap('esc')
	}

	generateRandomString(length) {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
	}

	parseTime(time, pattern) {
		if (arguments.length === 0 || !time) {
			return null;
		}
		const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}';
		let date;
		if (typeof time === 'object') {
			date = time;
		} else {
			if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
				time = parseInt(time);
			} else if (typeof time === 'string') {
				time = time
					.replace(new RegExp(/-/gm), '/')
					.replace('T', ' ')
					.replace(new RegExp(/\.[\d]{3}/gm), '');
			}
			if (typeof time === 'number' && time.toString().length === 10) {
				time = time * 1000;
			}
			date = new Date(time);
		}


		date.setTime(date.getTime());

		const formatObj = {
			y: date.getFullYear(),
			m: date.getMonth() + 1,
			d: date.getDate(),
			h: date.getHours(),
			i: date.getMinutes(),
			s: date.getSeconds(),
			a: date.getDay()
		};
		return format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
			let value = formatObj[key];
			// Note: getDay() returns 0 on Sunday
			if (key === 'a') {
				return ['日', '一', '二', '三', '四', '五', '六'][value];
			}
			if (result.length > 0 && value < 10) {
				value = '0' + value;
			}
			return value || 0;
		});
	}  
	
	// 加日志
	addLog(traceId,msg,method){
	try {
		let res = this.httpForLog('POST','http://log.dev.shuzutech.com/sendLog?logKey=plume_log_list', [{
			"appName":"RPA机器",
			"serverName":"windows服务",
			"dtTime":new Date().getTime(),
			"traceId":traceId,
			"content":msg,
			"logLevel":"INFO",
			"className":method,
			"method":method,
			"logType":"1",
			"env": "prod",
			"dateTime": this.parseTime(new Date(),"{y}-{m}-{d} {h}:{i}:{s}")
		}] 
		, null , false)
		// console.log('日志发送结果:', res);
		// console.log('addLog 方法执行完毕');
	} catch (error) {
		console.log('回调失败:', error);
	}
	}
	// 上传PDF加日志
	addLogForPDF(traceId,msg,method){
	try {
		let res = this.httpForLog('POST','http://log.dev.shuzutech.com/sendLog?logKey=plume_log_list', [{
			"appName":"RPA上传PDF文件",
			"serverName":"windows服务",
			"dtTime":new Date().getTime(),
			"traceId":traceId,
			"content":msg,
			"logLevel":"INFO",
			"className":method,
			"method":method,
			"logType":"1",
			"env": "prod",
			"dateTime": this.parseTime(new Date(),"{y}-{m}-{d} {h}:{i}:{s}")
		}] 
		, null , false)
		// console.log('日志发送结果:', res);
		// console.log('addLog 方法执行完毕');
		} catch (error) {
			console.log('回调失败:', error);
		}
	}

	// 登录时弹窗确认
	loginConfirm(){
		for(let i = 0; i < 10 ; i ++){
			console.log('首次登录税局弹窗')
			let loginConfirm = pbottleRPA.browserCMD_click(`div[aria-label='用户协议']>div.el-dialog__footer>div>button span:contains(确认)`)
			console.log('loginConfirm', loginConfirm)
			if(loginConfirm === 'ok') return
		}
	}
	
	// 获取移动距离和图片大小
	getDistanceAndSize(flowId){
		let rs = this.http('GET', `flow/operate/checkSlider`, null,{
			'flowId':flowId,
			'taskKey':'user_verify',
		}, true)
		console.log('获取移动距离和图片大小:', rs)
		return rs
	}

	// Old-过去滑块接口
	getCapDetial(target,back){
		let rs = this.httpforCL('POST','getCapDetial',{
			'target':target,
			'back':back
		},null,false)
		// console.log('获取缺口位置:', rs)
		return rs	
	}

	// tests
	recognizeAllText(image){
		let rs = this.httpforCL('POST','recognizeAllText',{
			'image':image,
		},null,false)
		// console.log('获取缺口位置:', rs)
		return rs	
	}
	
	// New-新滑块接口
	getRotateDistance(target,back){
		let rs = this.httpforCL('POST','getRotateDistance',{
			'target':target,
			'back':back
		},null,false)
		// console.log('获取缺口位置:', rs)
		return rs	
	}
	
	recognizeText1(image){
		let rs = this.httpforCLTest('POST','recognizeText',{
			'image':image,
		},null,false)
		// console.log('获取缺口位置:', rs)
		return rs	
	}

	//New-文字识别
	recognizeText(userInfo,base64){
		let rs = this.ChaoJiYing('POST',{
			user:userInfo.user,
			pass:userInfo.pass,
			softif:userInfo.softid,
			codetype:'9800',
			'file_base64': base64
		},null,false)
		// console.log('获取缺口位置:', rs)
		return rs	
	}


	recognizeText1(userInfo,filename){
		let rs = this.ChaoJiYing('POST',{
			user:userInfo.user,
			pass:userInfo.pass,
			softif:userInfo.softid,
			codetype:'9104',
			'userfile': rest.file(filename, null, fs.statSync(filename).size, null, 'image/png')
		},null,false)
		// console.log('获取缺口位置:', rs)
		return rs	
	}

	// 路飞本地测试接口
	// getRotateDistance(target,back){
	// 	let rs = this.httpforCLTest('POST','getRotateDistance',{
	// 		'target':target,
	// 		'back':back
	// 	},null,false)
	// 	// console.log('获取缺口位置:', rs)
	// 	return rs	
	// }	

	
	// let res = this.http('POST', 'flow/operate/completeTask', {
	// 		'id': id,
	// 		'flowId': flowId,
	// 		'flowKey': flowKey,
	// 		'taskKey': taskKey,
	// 		'variable': typeof variable === 'string' ? JSON.parse(variable) : variable,
	// 		"result": result,
	// 		"message": message,
	// 	}, null, false)
	// 	this.addLog(global.traceId, `完成任务的message:${message},后台完成接口响应结果:${JSON.stringify(res)}`, `${taskKey}`)
	// 	console.log('完成结果', JSON.stringify(res))
	// 	return res
}
module.exports = Untils;
