{"name": "xml2js", "description": "Simple XML to JavaScript object converter.", "keywords": ["xml", "json"], "homepage": "https://github.com/<PERSON><PERSON>-from-XIV/node-xml2js", "version": "0.4.0", "author": "<PERSON><PERSON> <<EMAIL>> (http://xivilization.net)", "contributors": ["maqr <<EMAIL>> (https://github.com/maqr)", "<PERSON> (http://benweaver.com/)", "<PERSON><PERSON> (https://github.com/jaek<PERSON>)", "<PERSON>", "<PERSON><PERSON><PERSON> (http://www.saltwaterc.eu/)", "<PERSON> <<EMAIL>> (http://cartercole.com/)", "<PERSON> <<EMAIL>> (http://www.kurtraschke.com/)", "Contra <<EMAIL>> (https://github.com/Contra)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/mdiniz)", "<PERSON> (https://github.com/mhart)", "<PERSON> <<EMAIL>> (http://zacharyscott.net/)", "<PERSON> (https://github.com/raoul<PERSON><PERSON>)", "Salsita Software (http://www.salsitasoft.com/)", "<PERSON> <<EMAIL>> (http://www.emotive.com/)", "<PERSON> <<EMAIL>> (http://weibo.com/shyvo)", "<PERSON> <<EMAIL>> (https://github.com/Sitin)", "<PERSON> <<EMAIL>> (https://github.com/christav)", "<PERSON> <<EMAIL>> (http://f2e.us/)", "<PERSON> <<EMAIL>> (http://www.bitstorm.it/)", "<PERSON> (http://jacksenechal.com/)", "<PERSON> <<EMAIL>> (https://github.com/hoelzl)"], "main": "./lib/xml2js", "directories": {"lib": "./lib"}, "scripts": {"test": "zap"}, "repository": {"type": "git", "url": "https://github.com/<PERSON><PERSON>-from-XIV/node-xml2js.git"}, "dependencies": {"sax": "0.5.x", "xmlbuilder": ">=0.4.2"}, "devDependencies": {"coffee-script": ">=1.6.3", "zap": ">=0.2.5", "docco": ">=0.6.2", "diff": ">=1.0.7"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/Leon<PERSON>-from-XIV/node-xml2js/master/LICENSE"}]}