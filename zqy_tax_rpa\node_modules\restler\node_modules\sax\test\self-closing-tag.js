
require(__dirname).test({
  xml :
  "<root>   "+
    "<haha /> "+
    "<haha/>  "+
    "<monkey> "+
      "=(|)     "+
    "</monkey>"+
  "</root>  ",
  expect : [
    ["opentag", {name:"RO<PERSON>", attributes:{}, isSelfClosing: false}],
    ["opentag", {name:"<PERSON><PERSON><PERSON>", attributes:{}, isSelfClosing: true}],
    ["closetag", "HAHA"],
    ["opentag", {name:"<PERSON><PERSON><PERSON>", attributes:{}, isSelfClosing: true}],
    ["closetag", "H<PERSON><PERSON>"],
    // ["opentag", {name:"<PERSON><PERSON><PERSON>", attributes:{}}],
    // ["closetag", "<PERSON><PERSON><PERSON>"],
    ["opentag", {name:"<PERSON><PERSON><PERSON><PERSON>", attributes:{}, isSelfClosing: false}],
    ["text", "=(|)"],
    ["closetag", "MONKE<PERSON>"],
    ["closetag", "ROOT"]
  ],
  opt : { trim : true }
});