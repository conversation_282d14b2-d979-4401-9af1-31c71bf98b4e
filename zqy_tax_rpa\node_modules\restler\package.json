{"name": "restler", "version": "3.4.0", "description": "An HTTP client library for node.js", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/danwrong/restler", "repository": {"type": "git", "url": "https://github.com/danwrong/restler.git"}, "directories": {"lib": "./lib"}, "main": "./lib/restler", "engines": {"node": ">= 0.10.x"}, "scripts": {"test": "node test/all.js"}, "dependencies": {"qs": "1.2.0", "xml2js": "0.4.0", "yaml": "0.2.3", "iconv-lite": "0.2.11"}, "devDependencies": {"nodeunit": "0.8.2"}, "license": "MIT"}